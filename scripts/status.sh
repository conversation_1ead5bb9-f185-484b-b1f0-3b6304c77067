#!/bin/bash

# Admin Backend Status Check Script
# Run this script to check the status of your deployment

set -e

APP_NAME="admin-backend"
APP_DIR="/opt/$APP_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "ok")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

echo "🔍 Admin Backend Status Check"
echo "=============================="

# Check if running in correct directory
if [ -d "$APP_DIR" ]; then
    cd $APP_DIR
    print_status "ok" "Application directory found: $APP_DIR"
else
    print_status "error" "Application directory not found: $APP_DIR"
    exit 1
fi

# Check Docker
if command -v docker &> /dev/null; then
    print_status "ok" "Docker is installed: $(docker --version)"
else
    print_status "error" "Docker is not installed"
fi

# Check Docker Compose
if command -v docker-compose &> /dev/null; then
    print_status "ok" "Docker Compose is installed: $(docker-compose --version)"
else
    print_status "error" "Docker Compose is not installed"
fi

# Check if containers are running
echo ""
echo "📊 Container Status:"
if [ -f "docker-compose.prod.yml" ]; then
    docker-compose -f docker-compose.prod.yml ps
else
    print_status "warning" "docker-compose.prod.yml not found"
fi

# Check application health
echo ""
echo "🏥 Health Check:"
if curl -f -s http://localhost:8080/api/health > /dev/null; then
    print_status "ok" "Application is responding to health checks"
    echo "   Response: $(curl -s http://localhost:8080/api/health | jq -r '.status // "unknown"' 2>/dev/null || echo "unknown")"
else
    print_status "error" "Application health check failed"
fi

# Check Nginx status
echo ""
echo "🌐 Nginx Status:"
if systemctl is-active --quiet nginx; then
    print_status "ok" "Nginx is running"
else
    print_status "error" "Nginx is not running"
fi

# Check disk space
echo ""
echo "💾 Disk Usage:"
df -h / | tail -1 | awk '{
    usage = $5
    gsub(/%/, "", usage)
    if (usage > 90) 
        print "❌ Disk usage critical: " usage "%"
    else if (usage > 80)
        print "⚠️  Disk usage high: " usage "%"
    else
        print "✅ Disk usage normal: " usage "%"
}'

# Check memory usage
echo ""
echo "🧠 Memory Usage:"
free -h | awk 'NR==2{
    used = $3
    total = $2
    percent = ($3/$2) * 100
    if (percent > 90)
        print "❌ Memory usage critical: " used "/" total " (" int(percent) "%)"
    else if (percent > 80)
        print "⚠️  Memory usage high: " used "/" total " (" int(percent) "%)"
    else
        print "✅ Memory usage normal: " used "/" total " (" int(percent) "%)"
}'

# Check recent logs for errors
echo ""
echo "📝 Recent Logs (last 10 lines):"
if [ -f "logs/monitor.log" ]; then
    tail -10 logs/monitor.log
else
    print_status "warning" "Monitor log file not found"
fi

# Check last deployment
echo ""
echo "🚀 Last Deployment:"
if [ -f ".git/logs/HEAD" ]; then
    last_commit=$(git log -1 --format="%h - %s (%cr)" 2>/dev/null || echo "Unable to get commit info")
    print_status "info" "Last commit: $last_commit"
else
    print_status "warning" "Git information not available"
fi

# Show running processes
echo ""
echo "🔄 Running Processes:"
ps aux | grep -E "(node|docker)" | grep -v grep | head -5

echo ""
echo "=============================="
echo "Status check completed!"
