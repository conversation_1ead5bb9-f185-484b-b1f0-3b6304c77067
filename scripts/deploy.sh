#!/bin/bash

# Admin Backend Deployment Script for EC2
# This script handles the deployment of the NestJS application

set -e  # Exit on any error

APP_NAME="admin-backend"
APP_DIR="/opt/$APP_NAME"
SERVICE_NAME="admin-backend"
DOCKER_IMAGE="$APP_NAME:latest"

echo "🚀 Starting deployment of $APP_NAME..."

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if service exists
service_exists() {
    systemctl list-units --full -all | grep -Fq "$1.service"
}

# Navigate to app directory
cd $APP_DIR

log "📁 Current directory: $(pwd)"

# Check if .env file exists, if not copy from .env.example
if [ ! -f .env ]; then
    log "📋 Creating .env file from .env.example"
    cp .env.example .env
    log "⚠️  Please update .env file with production values"
fi

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    log "🐳 Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    sudo systemctl start docker
    sudo systemctl enable docker
fi

# Install Docker Compose if not present
if ! command -v docker-compose &> /dev/null; then
    log "🐳 Installing Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# Stop existing containers
log "🛑 Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down || true

# Remove old images
log "🗑️  Removing old Docker images..."
docker image prune -f || true

# Build new image
log "🔨 Building Docker image..."
docker build -f Dockerfile.prod -t $DOCKER_IMAGE .

# Start services
log "🚀 Starting services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
log "⏳ Waiting for services to start..."
sleep 30

# Health check
log "🏥 Performing health check..."
for i in {1..10}; do
    if curl -f http://localhost:8080/api/health &> /dev/null; then
        log "✅ Health check passed!"
        break
    else
        log "⏳ Health check attempt $i/10 failed, retrying in 10 seconds..."
        sleep 10
    fi
    
    if [ $i -eq 10 ]; then
        log "❌ Health check failed after 10 attempts"
        docker-compose -f docker-compose.prod.yml logs
        exit 1
    fi
done

# Show running containers
log "📊 Running containers:"
docker-compose -f docker-compose.prod.yml ps

log "✅ Deployment completed successfully!"
log "🌐 Application is running at: http://$(curl -s ifconfig.me):8080"
