#!/bin/bash

# EC2 Initial Setup Script for Admin Backend Deployment
# Run this script once on your EC2 instance to prepare it for deployments

set -e

echo "🚀 Setting up EC2 instance for Admin Backend deployment..."

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Update system packages
log "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential packages
log "📦 Installing essential packages..."
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Node.js 20
log "📦 Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Verify Node.js installation
log "✅ Node.js version: $(node --version)"
log "✅ NPM version: $(npm --version)"

# Install Docker
log "🐳 Installing Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
sudo systemctl start docker
sudo systemctl enable docker

# Install Docker Compose
log "🐳 Installing Docker Compose..."
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify Docker installation
log "✅ Docker version: $(docker --version)"
log "✅ Docker Compose version: $(docker-compose --version)"

# Create application directory
log "📁 Creating application directory..."
sudo mkdir -p /opt/admin-backend
sudo chown $USER:$USER /opt/admin-backend

# Install PM2 for process management (alternative to Docker if needed)
log "📦 Installing PM2..."
sudo npm install -g pm2

# Setup firewall
log "🔥 Configuring firewall..."
sudo ufw allow ssh
sudo ufw allow 8080/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Create swap file (recommended for small instances)
log "💾 Creating swap file..."
if [ ! -f /swapfile ]; then
    sudo fallocate -l 2G /swapfile
    sudo chmod 600 /swapfile
    sudo mkswap /swapfile
    sudo swapon /swapfile
    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
fi

# Install Nginx (for reverse proxy)
log "🌐 Installing Nginx..."
sudo apt install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Create Nginx configuration for the app
log "🌐 Configuring Nginx..."
sudo tee /etc/nginx/sites-available/admin-backend > /dev/null <<EOF
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /health {
        proxy_pass http://localhost:8080/api/health;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/admin-backend /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx

# Setup log rotation
log "📝 Setting up log rotation..."
sudo tee /etc/logrotate.d/admin-backend > /dev/null <<EOF
/opt/admin-backend/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
}
EOF

# Create logs directory
mkdir -p /opt/admin-backend/logs

# Setup monitoring script
log "📊 Creating monitoring script..."
sudo tee /usr/local/bin/admin-backend-monitor.sh > /dev/null <<'EOF'
#!/bin/bash
# Simple monitoring script for admin-backend

APP_URL="http://localhost:8080/api/health"
LOG_FILE="/opt/admin-backend/logs/monitor.log"

check_health() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    if curl -f -s "$APP_URL" > /dev/null; then
        echo "[$timestamp] ✅ Application is healthy" >> "$LOG_FILE"
        return 0
    else
        echo "[$timestamp] ❌ Application health check failed" >> "$LOG_FILE"
        return 1
    fi
}

# Run health check
if ! check_health; then
    echo "[$timestamp] 🔄 Attempting to restart application..." >> "$LOG_FILE"
    cd /opt/admin-backend
    docker-compose -f docker-compose.prod.yml restart
fi
EOF

sudo chmod +x /usr/local/bin/admin-backend-monitor.sh

# Setup cron job for monitoring
log "⏰ Setting up monitoring cron job..."
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/admin-backend-monitor.sh") | crontab -

# Setup GitHub Actions runner user (optional)
log "👤 Creating deployment user..."
sudo useradd -m -s /bin/bash deploy || true
sudo usermod -aG docker deploy
sudo mkdir -p /home/<USER>/.ssh
sudo chown deploy:deploy /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh

log "✅ EC2 setup completed successfully!"
log "📋 Next steps:"
log "   1. Add your GitHub Actions public key to /home/<USER>/.ssh/authorized_keys"
log "   2. Set up your GitHub repository secrets:"
log "      - EC2_HOST: $(curl -s ifconfig.me)"
log "      - EC2_USERNAME: deploy"
log "      - EC2_PRIVATE_KEY: (your private key)"
log "   3. Copy .env.production to /opt/admin-backend/.env and update values"
log "   4. Push to main branch to trigger deployment"
log ""
log "🌐 Your server will be accessible at: http://$(curl -s ifconfig.me)"
