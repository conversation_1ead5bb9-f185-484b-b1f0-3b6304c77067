import mongoose, { PaginateModel } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IVisitorLogRepository, VisitorLogEntity } from '@app/repository';
import { VisitorLogDocument } from '@app/db/portfolio-db/schemas/visitor-log.schema';

export class VisitorLogRepository implements IVisitorLogRepository {
  constructor(
    private readonly visitorLogRepository: PaginateModel<VisitorLogDocument>,
  ) {}

  isConnected(): void {
    if (this.visitorLogRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: VisitorLogEntity): Promise<VisitorLogEntity> {
    const insert = await new this.visitorLogRepository(
      this.toSchema(item),
    ).save();
    return this.toEntity(insert.toObject());
  }

  async findById(id: string): Promise<VisitorLogEntity> {
    return this.toEntity(await this.visitorLogRepository.findById(id));
  }

  async findOne(filter: Partial<VisitorLogEntity>): Promise<VisitorLogEntity> {
    return this.toEntity(
      await this.visitorLogRepository.findOne(this.toFilter(filter)),
    );
  }

  async findAll(
    filter: Partial<VisitorLogEntity>,
    options?: any,
  ): Promise<VisitorLogEntity[]> {
    const query = this.visitorLogRepository.find(this.toFilter(filter));

    if (options?.sort) {
      query.sort(options.sort);
    }
    if (options?.limit) {
      query.limit(options.limit);
    }
    if (options?.skip) {
      query.skip(options.skip);
    }

    return (await query).map((item) => this.toEntity(item));
  }

  async update(
    filter: Partial<VisitorLogEntity>,
    item: Partial<VisitorLogEntity>,
  ): Promise<VisitorLogEntity> {
    return this.toEntity(
      await this.visitorLogRepository.findOneAndUpdate(
        this.toFilter(filter),
        this.toUpdate(item),
        {
          new: true,
        },
      ),
    );
  }

  async updateMany(
    filter: Partial<VisitorLogEntity>,
    items: Partial<VisitorLogEntity>[],
  ): Promise<VisitorLogEntity[]> {
    await this.visitorLogRepository.updateMany(
      this.toFilter(filter),
      items.map((item) => this.toUpdate(item)),
    );
    return this.findAll(filter);
  }

  async deleteOne(
    filter: Partial<VisitorLogEntity>,
  ): Promise<VisitorLogEntity> {
    const document = await this.visitorLogRepository.findOne(
      this.toFilter(filter),
    );
    await this.visitorLogRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }

  async deleteMany(
    filter: Partial<VisitorLogEntity>,
  ): Promise<VisitorLogEntity[]> {
    const documents = await this.visitorLogRepository.find(
      this.toFilter(filter),
    );
    await this.visitorLogRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<VisitorLogEntity>): Promise<number> {
    return this.visitorLogRepository.countDocuments(this.toFilter(filter));
  }

  async getVisitorStats(
    startDate?: Date,
    endDate?: Date,
  ): Promise<{
    totalVisits: number;
    uniqueVisitors: number;
    topEndpoints: Array<{ endpoint: string; count: number }>;
    topCountries: Array<{ country: string; count: number }>;
  }> {
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter['visitedAt'] = {};
      if (startDate) dateFilter['visitedAt']['$gte'] = startDate;
      if (endDate) dateFilter['visitedAt']['$lte'] = endDate;
    }

    const [totalVisits, uniqueVisitors, topEndpoints, topCountries] =
      await Promise.all([
        this.visitorLogRepository.countDocuments(dateFilter),
        this.visitorLogRepository
          .distinct('ipAddress', dateFilter)
          .then((ips) => ips.length),
        this.visitorLogRepository.aggregate([
          { $match: dateFilter },
          { $group: { _id: '$endpoint', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
          { $project: { endpoint: '$_id', count: 1, _id: 0 } },
        ]),
        this.visitorLogRepository.aggregate([
          { $match: { ...dateFilter, country: { $exists: true, $ne: null } } },
          { $group: { _id: '$country', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
          { $project: { country: '$_id', count: 1, _id: 0 } },
        ]),
      ]);

    return {
      totalVisits,
      uniqueVisitors,
      topEndpoints,
      topCountries,
    };
  }

  async getVisitorsByTimeRange(
    startDate: Date,
    endDate: Date,
    groupBy: 'hour' | 'day' | 'month' = 'day',
  ): Promise<Array<{ date: string; count: number }>> {
    const groupFormat = {
      hour: { $dateToString: { format: '%Y-%m-%d %H:00', date: '$visitedAt' } },
      day: { $dateToString: { format: '%Y-%m-%d', date: '$visitedAt' } },
      month: { $dateToString: { format: '%Y-%m', date: '$visitedAt' } },
    };

    return this.visitorLogRepository.aggregate([
      {
        $match: {
          visitedAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: groupFormat[groupBy],
          count: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $project: {
          date: '$_id',
          count: 1,
          _id: 0,
        },
      },
    ]);
  }

  private toFilter(document: Partial<VisitorLogEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && {
        user:
          typeof user === 'string'
            ? new mongoose.Types.ObjectId(user)
            : new mongoose.Types.ObjectId(user.id),
      }),
    };
  }

  private toUpdate(item: Partial<VisitorLogEntity>) {
    const { user, ...rest } = item;
    return {
      ...rest,
      ...(user && {
        user:
          typeof user === 'string'
            ? new mongoose.Types.ObjectId(user)
            : new mongoose.Types.ObjectId(user.id),
      }),
    };
  }

  private toEntity(document: VisitorLogDocument): VisitorLogEntity {
    if (!document) return null;

    const visitorLog = new VisitorLogEntity();
    visitorLog.id = document._id.toString();
    visitorLog.endpoint = document.endpoint;
    visitorLog.method = document.method;
    visitorLog.ipAddress = document.ipAddress;
    visitorLog.userAgent = document.userAgent;
    visitorLog.referer = document.referer;
    visitorLog.user = document.user ? document.user.toString() : null; // Store as user ID string
    visitorLog.visitedAt = document.visitedAt;
    visitorLog.responseTime = document.responseTime;
    visitorLog.statusCode = document.statusCode;
    visitorLog.country = document.country;
    visitorLog.city = document.city;
    visitorLog.createdAt = document.createdAt;
    visitorLog.updatedAt = document.updatedAt;

    return visitorLog;
  }

  private toSchema(visitorLogEntity: VisitorLogEntity): VisitorLogDocument {
    return new this.visitorLogRepository({
      ...visitorLogEntity,
      user:
        visitorLogEntity.user &&
        (typeof visitorLogEntity.user === 'string'
          ? new mongoose.Types.ObjectId(visitorLogEntity.user)
          : new mongoose.Types.ObjectId(visitorLogEntity.user.id)),
    });
  }
}
