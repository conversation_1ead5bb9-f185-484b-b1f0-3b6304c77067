#!/bin/bash

# Find all layout files except the root layout
find portfolio/app -name "layout.js" -not -path "portfolio/app/layout.js" | while read -r file; do
  echo "Fixing $file"
  # Replace the content of the file
  cat > "$file" << 'EOF'
"use client";
import MobileMenu from "@/components/MobileMenu/MobileMenu";
import Footer from "@/components/shared/Footer/Footer";
import Navbar from "@/components/shared/Navbar/Navbar";

export default function Layout({ children }) {
  return (
    <div id="__nuxt" className="h-full w-full">
      <MobileMenu />
      <Navbar />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  );
}
EOF
done

echo "All layout files have been fixed."
