import { Controller, Get, Logger, Query, UseGuards } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import {
  AnalyticsQueryDto,
  DashboardAnalyticsDto,
  DetailedAnalyticsDto,
  FrontendDomainStatsDto,
  TimeRangeAnalyticsDto,
  TimeSeriesDataDto,
  VisitorStatsDto,
} from './dto';
import {
  JwtAuthGuard,
  PERMISSIONS,
  PermissionsGuard,
  RequirePermissions,
  RequireRoles,
  ROLE,
} from '@app/common';

@Controller('analytics')
@UseGuards(JwtAuthGuard)
export class AnalyticsController {
  private readonly logger = new Logger(AnalyticsController.name);

  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('visitor-stats')
  @UseGuards(PermissionsGuard)
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR)
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  async getVisitorStats(
    @Query() query: AnalyticsQueryDto,
  ): Promise<VisitorStatsDto> {
    this.logger.log('Getting visitor stats');
    return this.analyticsService.getVisitorStats(query);
  }

  @Get('detailed')
  @UseGuards(PermissionsGuard)
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR)
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  async getDetailedAnalytics(
    @Query() query: AnalyticsQueryDto,
  ): Promise<DetailedAnalyticsDto> {
    this.logger.log('Getting detailed analytics');
    return this.analyticsService.getDetailedAnalytics(query);
  }

  @Get('time-series')
  @UseGuards(PermissionsGuard)
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR)
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  async getTimeSeriesData(
    @Query() query: TimeRangeAnalyticsDto,
  ): Promise<TimeSeriesDataDto[]> {
    this.logger.log('Getting time series data');
    return this.analyticsService.getTimeSeriesData(query);
  }

  @Get('frontend-domains')
  @UseGuards(PermissionsGuard)
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR)
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  async getFrontendDomainStats(
    @Query() query: AnalyticsQueryDto,
  ): Promise<FrontendDomainStatsDto[]> {
    this.logger.log('Getting frontend domain stats');
    return this.analyticsService.getFrontendDomainStats(query);
  }

  @Get('dashboard')
  @UseGuards(PermissionsGuard)
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR)
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  async getDashboardAnalytics(
    @Query() query: AnalyticsQueryDto,
  ): Promise<DashboardAnalyticsDto> {
    this.logger.log('Getting dashboard analytics');
    return this.analyticsService.getDashboardAnalytics(query);
  }

  @Get('realtime')
  @UseGuards(PermissionsGuard)
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR)
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  async getRealtimeStats(): Promise<{
    activeVisitors: number;
    recentVisits: Array<{
      endpoint: string;
      method: string;
      ipAddress: string;
      frontendDomain: string;
      visitedAt: Date;
      statusCode: number;
    }>;
  }> {
    this.logger.log('Getting realtime stats');
    return this.analyticsService.getRealtimeStats();
  }
}
