import { useState } from 'react'
import type { NotificationState, UserFormData } from '../types'
import { validateImageFile } from '../utils/imageUtils'

interface UseFileUploadProps {
  formData: UserFormData
  setFormData: (data: UserFormData) => void
  imgSrc: string
  setImgSrc: (src: string) => void
  setNotification: (notification: NotificationState) => void
  originalImageSrc: string // Track the original image from database
}

export const useFileUpload = ({
  formData,
  setFormData,
  imgSrc,
  setImgSrc,
  setNotification,
  originalImageSrc
}: UseFileUploadProps) => {
  const [fileInput, setFileInput] = useState<string>('')
  const [uploading, setUploading] = useState<boolean>(false)
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null)

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = event.target

    if (files && files.length !== 0) {
      const selectedFile = files[0]

      // Validate file
      const validation = validateImageFile(selectedFile)
      if (!validation.isValid) {
        setNotification({
          show: true,
          message: validation.error || 'Invalid file',
          type: 'error'
        })
        return
      }

      // Store the actual file for FormData
      setProfileImageFile(selectedFile)
      setFileInput(event.target.value)

      // Read and display the image preview immediately
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // Update both imgSrc (for immediate preview) and tempImgSrc (for cropper)
        setImgSrc(result)
        setTempImgSrc(result)
        // Also update the formData for consistency
        setFormData({ ...formData, profileImage: result })
      }
      reader.readAsDataURL(selectedFile)

      // Show success notification
      setNotification({
        show: true,
        message: 'Image uploaded successfully! You can now crop it if needed.',
        type: 'success'
      })
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/1.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-profile-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setProfileImageFile(croppedFile)

    // Create a preview URL for the cropped image and update all relevant states
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // Update imgSrc for immediate preview
      setImgSrc(result)
      // Update tempImgSrc for future cropping
      setTempImgSrc(result)
      // Update formData for consistency
      setFormData({ ...formData, profileImage: result })
    }
    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)

    setNotification({
      show: true,
      message: 'Profile image cropped successfully!',
      type: 'success'
    })
  }

  const handleFileInputReset = () => {
    console.log('Reset clicked - originalImageSrc:', originalImageSrc)
    console.log('Current imgSrc:', imgSrc)
    console.log('Is original image default?', originalImageSrc === '/images/avatars/1.png')

    setFileInput('')
    // Restore the original image from database, not default avatar
    setImgSrc(originalImageSrc)
    setTempImgSrc(originalImageSrc)
    setProfileImageFile(null)
    // Update formData to reflect the original image
    setFormData({ ...formData, profileImage: originalImageSrc })

    const isDefaultImage = originalImageSrc === '/images/avatars/1.png'
    setNotification({
      show: true,
      message: isDefaultImage ? 'Image reset to default avatar.' : 'Image reset to your original profile picture.',
      type: 'success'
    })
  }

  return {
    fileInput,
    uploading,
    cropperOpen,
    tempImgSrc,
    profileImageFile,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen
  }
}
