'use client'

// React Imports
import { useSearchParams } from 'next/navigation'

// Component Imports
import SkillsForm from './SkillsForm'
import SkillsView from './SkillsView'

const SkillsDetails = () => {
  // Hooks
  const searchParams = useSearchParams()
  const mode = searchParams.get('mode')
  const id = searchParams.get('id')
  const isViewMode = mode === 'view'

  // Render the appropriate component based on mode
  return isViewMode ? <SkillsView /> : <SkillsForm />
}

export default SkillsDetails
