name: Deploy to EC2

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '20'
  EC2_HOST: ${{ secrets.EC2_HOST }}
  EC2_USERNAME: ${{ secrets.EC2_USERNAME }}
  EC2_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
  APP_NAME: admin-backend

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linter
      run: npm run lint

    - name: Run tests
      run: npm run test

    - name: Build application
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.EC2_PRIVATE_KEY }}

    - name: Add EC2 to known hosts
      run: |
        ssh-keyscan -H ${{ secrets.EC2_HOST }} >> ~/.ssh/known_hosts

    - name: Deploy to EC2
      run: |
        # Create deployment directory on EC2
        ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
          sudo mkdir -p /opt/${{ env.APP_NAME }}
          sudo chown ${{ secrets.EC2_USERNAME }}:${{ secrets.EC2_USERNAME }} /opt/${{ env.APP_NAME }}
        "

        # Copy files to EC2
        rsync -avz --delete \
          --exclude 'node_modules' \
          --exclude '.git' \
          --exclude 'coverage' \
          --exclude '.env' \
          ./ ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }}:/opt/${{ env.APP_NAME }}/

        # Run deployment script on EC2
        ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
          cd /opt/${{ env.APP_NAME }}
          chmod +x scripts/deploy.sh
          ./scripts/deploy.sh
        "

    - name: Health Check
      run: |
        sleep 30
        curl -f http://${{ secrets.EC2_HOST }}:8080/api/health || exit 1
