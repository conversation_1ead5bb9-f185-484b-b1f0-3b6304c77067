#!/bin/bash

# SSH Key Setup Script for GitHub Actions Deployment
# Run this script on your local machine

set -e

echo "🔑 Setting up SSH keys for GitHub Actions deployment"
echo "=================================================="

# Variables
KEY_NAME="github-actions-deploy"
KEY_PATH="$HOME/.ssh/$KEY_NAME"
EC2_USER="deploy"

# Prompt for EC2 IP
read -p "Enter your EC2 instance IP address: " EC2_IP

if [ -z "$EC2_IP" ]; then
    echo "❌ EC2 IP address is required"
    exit 1
fi

# Generate SSH key pair
echo "🔐 Generating SSH key pair..."
ssh-keygen -t rsa -b 4096 -C "github-actions@portfolio-deployment" -f "$KEY_PATH" -N ""

echo "✅ SSH key pair generated:"
echo "   Private key: $KEY_PATH"
echo "   Public key: $KEY_PATH.pub"

# Display public key
echo ""
echo "📋 Public key content (copy this to EC2):"
echo "=========================================="
cat "$KEY_PATH.pub"
echo "=========================================="

# Display private key for GitHub secret
echo ""
echo "🔒 Private key content (copy this to GitHub secret EC2_PRIVATE_KEY):"
echo "====================================================================="
cat "$KEY_PATH"
echo "====================================================================="

# Instructions for copying to EC2
echo ""
echo "📝 Next steps:"
echo "1. Copy the public key to your EC2 instance:"
echo "   ssh-copy-id -i $KEY_PATH.pub $EC2_USER@$EC2_IP"
echo ""
echo "2. Or manually add it to EC2:"
echo "   ssh $EC2_USER@$EC2_IP"
echo "   mkdir -p ~/.ssh"
echo "   echo 'PUBLIC_KEY_CONTENT' >> ~/.ssh/authorized_keys"
echo "   chmod 600 ~/.ssh/authorized_keys"
echo "   chmod 700 ~/.ssh"
echo ""
echo "3. Add these GitHub secrets:"
echo "   EC2_HOST: $EC2_IP"
echo "   EC2_USERNAME: $EC2_USER"
echo "   EC2_PRIVATE_KEY: (the private key content shown above)"
echo ""
echo "4. Test the connection:"
echo "   ssh -i $KEY_PATH $EC2_USER@$EC2_IP"

# Offer to copy public key to EC2
echo ""
read -p "Do you want to copy the public key to EC2 now? (y/n): " COPY_KEY

if [ "$COPY_KEY" = "y" ] || [ "$COPY_KEY" = "Y" ]; then
    echo "🚀 Copying public key to EC2..."
    ssh-copy-id -i "$KEY_PATH.pub" "$EC2_USER@$EC2_IP"
    
    echo "✅ Testing connection..."
    if ssh -i "$KEY_PATH" -o ConnectTimeout=10 "$EC2_USER@$EC2_IP" "echo 'Connection successful!'" 2>/dev/null; then
        echo "✅ SSH connection test successful!"
    else
        echo "❌ SSH connection test failed. Please check your EC2 configuration."
    fi
fi

echo ""
echo "🎉 SSH key setup completed!"
echo "Remember to add the private key content to GitHub secrets as EC2_PRIVATE_KEY"
