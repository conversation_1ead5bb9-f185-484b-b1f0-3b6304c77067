# Admin Backend Deployment Guide

This guide explains how to set up automated deployment of the admin-backend to EC2 using GitHub Actions.

## Overview

The deployment system uses:
- **GitHub Actions** for CI/CD pipeline
- **Docker** for containerization
- **Docker Compose** for orchestration
- **Nginx** as reverse proxy
- **MongoDB & Redis** as data stores

## Prerequisites

1. **EC2 Instance** (Ubuntu 20.04+ recommended)
2. **GitHub Repository** with admin-backend code
3. **Domain/IP** for your application

## Setup Instructions

### 1. Prepare EC2 Instance

SSH into your EC2 instance and run the setup script:

```bash
# Download and run the setup script
curl -fsSL https://raw.githubusercontent.com/your-username/your-repo/main/scripts/setup-ec2.sh -o setup-ec2.sh
chmod +x setup-ec2.sh
./setup-ec2.sh
```

This script will:
- Install Node.js, <PERSON><PERSON>, <PERSON><PERSON>, Nginx
- Configure firewall and swap
- Set up monitoring and log rotation
- Create application directories

### 2. Configure GitHub Secrets

In your GitHub repository, go to Settings > Secrets and Variables > Actions, and add:

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `EC2_HOST` | Your EC2 public IP or domain | `3.15.123.456` |
| `EC2_USERNAME` | SSH username | `deploy` |
| `EC2_PRIVATE_KEY` | SSH private key | `-----BEGIN RSA PRIVATE KEY-----...` |

### 3. Set Up SSH Key

Generate SSH key pair for GitHub Actions:

```bash
# On your local machine
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/github-actions

# Copy public key to EC2
ssh-copy-id -i ~/.ssh/github-actions.pub deploy@your-ec2-ip

# Add private key to GitHub secrets (EC2_PRIVATE_KEY)
cat ~/.ssh/github-actions
```

### 4. Configure Environment Variables

Copy the production environment template to your EC2 instance:

```bash
# On EC2 instance
cd /opt/admin-backend
cp .env.production .env

# Edit with your actual values
nano .env
```

Update these critical values:
- `MONGO_MAIN_DB_URL` - Your MongoDB connection string
- `MONGO_LOG_DB_URL` - Your MongoDB logs connection string
- `JWT_SECRET` - Strong secret for JWT tokens
- `CLOUDINARY_*` - Your Cloudinary credentials
- `REDIS_*` - Redis configuration

### 5. Test Deployment

Push to the main branch to trigger the first deployment:

```bash
git add .
git commit -m "feat: setup automated deployment"
git push origin main
```

## Deployment Process

When you push to the `main` branch:

1. **Test Phase**:
   - Checkout code
   - Install dependencies
   - Run linter
   - Run tests
   - Build application

2. **Deploy Phase** (only on main branch):
   - Setup SSH connection
   - Copy files to EC2
   - Run deployment script
   - Perform health check

## Monitoring

### Health Check
- Endpoint: `http://your-domain/api/health`
- Automated monitoring every 5 minutes
- Auto-restart on failure

### Logs
- Application logs: `/opt/admin-backend/logs/`
- Nginx logs: `/var/log/nginx/`
- Docker logs: `docker-compose -f docker-compose.prod.yml logs`

### Commands

```bash
# Check application status
cd /opt/admin-backend
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Restart application
docker-compose -f docker-compose.prod.yml restart

# Update application manually
git pull origin main
./scripts/deploy.sh
```

## Security Considerations

1. **Firewall**: Only ports 22, 80, 443, 8080 are open
2. **SSH**: Key-based authentication only
3. **Environment**: Sensitive data in .env file
4. **Updates**: Regular system updates via cron
5. **Monitoring**: Health checks and alerting

## Troubleshooting

### Deployment Fails
1. Check GitHub Actions logs
2. Verify SSH connection: `ssh deploy@your-ec2-ip`
3. Check EC2 disk space: `df -h`
4. Verify Docker status: `sudo systemctl status docker`

### Application Not Starting
1. Check logs: `docker-compose -f docker-compose.prod.yml logs`
2. Verify .env file exists and has correct values
3. Check database connectivity
4. Verify port 8080 is not in use: `sudo netstat -tlnp | grep 8080`

### Health Check Fails
1. Test manually: `curl http://localhost:8080/api/health`
2. Check application logs
3. Verify database connections
4. Check memory/CPU usage: `htop`

## Rollback

To rollback to a previous version:

```bash
cd /opt/admin-backend
git log --oneline  # Find commit hash
git checkout <commit-hash>
./scripts/deploy.sh
```

## SSL/HTTPS Setup (Optional)

To enable HTTPS with Let's Encrypt:

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal is set up automatically
```

## Performance Optimization

1. **Database**: Use MongoDB Atlas for better performance
2. **Caching**: Redis is configured for session/cache storage
3. **CDN**: Use Cloudinary for image optimization
4. **Monitoring**: Set up CloudWatch or similar monitoring

## Support

For issues or questions:
1. Check the logs first
2. Review this documentation
3. Check GitHub Issues
4. Contact the development team
