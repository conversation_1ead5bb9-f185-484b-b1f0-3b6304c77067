# Production Environment Configuration
# Copy this to .env on your EC2 instance and update with actual values

# Database Configuration
MONGO_MAIN_DB_CONNECTION_NAME=MONGO_MAIN_DB
MONGO_LOG_DB_CONNECTION_NAME=MONGO_LOG_DB
MONGO_MAIN_DB_URL=mongodb+srv://tanzim:<EMAIL>/portfolio
MONGO_LOG_DB_URL=mongodb+srv://tanzim:<EMAIL>/portfolio-log

# Server Configuration
PORT=8080
NODE_ENV=production

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=1d

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_CACHE_DB=0

# Cloudinary Configuration (Update with your actual values)
CLOUDINARY_CLOUD_NAME=dztzlaqdr
CLOUDINARY_API_KEY=442345682932359
CLOUDINARY_API_SECRET=wdHhhKjm3ouDZSKqnma7dAVwjfE

# CORS Configuration
CORS_ORIGIN=http://your-frontend-domain.com

# Logging
LOG_LEVEL=info

# Security
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
